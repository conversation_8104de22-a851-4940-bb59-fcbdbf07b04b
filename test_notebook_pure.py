#!/usr/bin/env python3
"""
测试完全按照notebook实现的宁银费率变更分析
"""

def test_pure_notebook_function():
    """测试纯notebook实现函数"""
    print("🧪 测试纯notebook实现函数...")
    
    try:
        from func import ningyin_fee_analysis_notebook_pure
        print("✅ ningyin_fee_analysis_notebook_pure 函数导入成功")
        
        # 测试不存在的文件
        result = ningyin_fee_analysis_notebook_pure("non_existent_file.pdf")
        if result == "":
            print("✅ 不存在文件返回空字符串")
            return True
        else:
            print(f"❌ 不存在文件应返回空字符串，实际返回: {result}")
            return False
            
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_v11_function():
    """测试V1.1函数"""
    print("🧪 测试V1.1函数...")
    
    try:
        from func import ningyin_fee_analysis_v11
        print("✅ ningyin_fee_analysis_v11 函数导入成功")
        
        # 测试不存在的文件
        result = ningyin_fee_analysis_v11("non_existent_file.pdf")
        
        # 检查返回结构
        expected_keys = ["product_info", "fee_changes", "effective_date", "announcement_date", "issuer"]
        
        for key in expected_keys:
            if key not in result:
                print(f"❌ 缺少必需字段: {key}")
                return False
        
        print("✅ V1.1函数结构正确")
        return True
        
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_notebook_components():
    """测试notebook核心组件"""
    print("🧪 测试notebook核心组件...")
    
    try:
        from func import ningyin_fee_analysis_notebook_pure
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_notebook_pure)
        
        # 检查notebook核心组件
        components = [
            "pdf_to_word_pdf2docx",
            "check_vertical_merge", 
            "fix_merge_table",
            "df_to_json_rows",
            "pdf_to_markdown",
            "uuid.uuid1().hex",
            "from pdf2docx import Converter",
            "docx.Document",
            "ffill()"
        ]
        
        missing = []
        for component in components:
            if component not in source:
                missing.append(component)
        
        if missing:
            print(f"❌ 缺少notebook组件: {missing}")
            return False
        
        print("✅ 所有notebook核心组件都存在")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_no_ai_in_pure():
    """测试纯notebook实现中没有AI调用"""
    print("🧪 测试纯notebook实现中没有AI调用...")
    
    try:
        from func import ningyin_fee_analysis_notebook_pure
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_notebook_pure)
        
        # 检查是否包含AI相关代码
        ai_keywords = [
            "ChatBot",
            "chat(",
            "openai",
            "temperature",
            "top_p",
            "max_tokens"
        ]
        
        found_ai = []
        for keyword in ai_keywords:
            if keyword in source:
                found_ai.append(keyword)
        
        if found_ai:
            print(f"❌ 纯notebook实现中包含AI代码: {found_ai}")
            return False
        
        print("✅ 纯notebook实现中没有AI调用")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_function_simplicity():
    """测试函数简洁性"""
    print("🧪 测试函数简洁性...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查函数是否简洁（行数不应该太多）
        lines = source.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        if len(non_empty_lines) > 50:
            print(f"⚠️  V1.1函数较复杂，有 {len(non_empty_lines)} 行代码")
        else:
            print(f"✅ V1.1函数简洁，有 {len(non_empty_lines)} 行代码")
        
        # 检查是否调用了纯notebook实现
        if "ningyin_fee_analysis_notebook_pure" in source:
            print("✅ V1.1函数正确调用了纯notebook实现")
            return True
        else:
            print("❌ V1.1函数没有调用纯notebook实现")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("测试完全按照notebook实现的宁银费率变更分析...")
    print("=" * 60)
    
    tests = [
        ("纯notebook函数测试", test_pure_notebook_function),
        ("V1.1函数测试", test_v11_function),
        ("Notebook组件测试", test_notebook_components),
        ("纯实现无AI测试", test_no_ai_in_pure),
        ("函数简洁性测试", test_function_simplicity)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！完全按照notebook实现")
        print("\n📋 实现确认:")
        print("✅ 创建了纯notebook实现函数")
        print("✅ V1.1函数调用纯notebook实现")
        print("✅ 包含所有notebook核心组件")
        print("✅ 纯实现中没有AI调用")
        print("✅ 函数结构简洁明了")
    else:
        print("⚠️  部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
