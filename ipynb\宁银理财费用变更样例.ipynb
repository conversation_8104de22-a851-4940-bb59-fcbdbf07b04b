import docx
import json
from docx.table import Table, _Cell, _Row
from docx.text.paragraph import Paragraph
import logging
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from copy import deepcopy
import re
from docx.oxml.ns import qn
import pandas as pd
import os
from pdf2docx import Converter
import uuid
import PyPDF2
import tqdm


def pdf_to_word_pdf2docx(pdf_path, word_path):
    cv = Converter(pdf_path)
    cv.convert(word_path, start=0, end=None)
    cv.close()

def check_vertical_merge(cell):
    tc = cell._tc
    tc_pr = tc.get_or_add_tcPr()
    vmerge = tc_pr.find(qn("w:vMerge"))
    
    if vmerge is not None:
        val = vmerge.get(qn("w:val"))
        # 特殊处理多个restart的情况
        if val == "restart":
            return "restart"
        elif val == "continue":
            return "continue"
    return "None"

def fix_merge_table(fix_table):
    new_talbe = pd.DataFrame()
    min_length = {}
    for table_i, table in fix_table.groupby('table_id'):
        if table_i == 0:
            new_talbe = table.copy()
            # 固化字段长度用以校验
            for col in table.columns:
                min_length[col] = table[col].iloc[:-1].astype(str).map(len).mean()
        else:
            first_row = table.iloc[0]
            # 计算字段达成率
            success_num = 0
            for col in table.columns:
                if len(str(first_row[col])) >= min_length[col]:
                    success_num += 1
            success_ratio = success_num / len(min_length)

            # 如果字段达成率低于70%则合并
            if success_ratio <= 0.7:
                for col in table.columns:
                    if col == 'table_id':
                        continue
                    fix_idx = new_talbe[new_talbe[col]!='!restart!'].iloc[-1].name
                    new_talbe.loc[fix_idx, col] += first_row[col].strip() if isinstance(first_row[col], str) else first_row[col]
                table = table.iloc[1:]
            new_talbe = pd.concat([new_talbe, table], ignore_index=True)
    fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')
    del fin_table['table_id']
    return fin_table

def df_to_json_rows(df):
    json_list = df.to_dict(orient='records')
    return json.dumps(json_list, indent=2, ensure_ascii=False)

def pdf_to_markdown(pdf_fn):
    temp_fn = f"{uuid.uuid1().hex}.docx"
    # 使用示例
    pdf_to_word_pdf2docx(pdf_fn, temp_fn)
    # 加载Word文档
    doc = docx.Document(temp_fn)
    out_markdown = ""

    table_list = []
    now_table = None
    header = []
    child_i = 0
    table_id = 0
    for child in doc.element.body:
    #     print(child_i, type(child))
        if isinstance(child, docx.oxml.text.paragraph.CT_P):  # 段落
            if child.text.replace('\n', '').strip() == '':
                continue
            if now_table is not None:
                print(child.text.replace('\n', '').strip())
                print("重置表格")
                out_markdown += "\n```json\n" + df_to_json_rows(fix_merge_table(now_table)) + "\n```\n"
                now_table = None
                header = []
                table_id = 0
            out_markdown += child.text.replace('\n', '').strip() + '\n'
        elif isinstance(child, docx.oxml.table.CT_Tbl): # 表格
            data = []
            table = Table(child, doc)
            last_tc = {}
            for row in table.rows:
                row_data = []
                cols_id = 0
                for cell in row.cells:
                    tc = cell._tc  # 获取底层 XML 元素
                    # 检查是否是合并单元格的后续行
                    if check_vertical_merge(cell) == "restart":
                        if last_tc.get(cols_id, None) is None:
                            last_tc[cols_id] = tc
                        else:
                            row_data.append('!restart!')  # 非首行合并单元格，输出空
                            continue

                    # 否则提取文本并清理
                    text = cell.text.strip().replace('\n', '').replace('\t', ' ')
                    row_data.append(text)
                    cols_id += 1
                data.append(row_data)

            if now_table is None:
                header = data[0]
                data = data[1:]
            out_df = pd.DataFrame(data, columns=header)
            out_df['table_id'] = table_id
            if now_table is None:
                now_table = out_df.copy()
            else:
                now_table = pd.concat([now_table, out_df])
            table_id += 1
        child_i += 1
    
    os.remove(temp_fn)
    return out_markdown

def is_scanned_pdf(pdf_path, threshold=0.9):
    """
    判断PDF是否为扫描件
    :param pdf_path: PDF文件路径
    :param threshold: 判断为扫描件的阈值（页面中可提取文本的比例）
    :return: True（扫描件）或 False（可转换文本）
    """
    try:
        # 方法1：检查PDF中是否包含可提取文本
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            total_pages = len(reader.pages)
            text_pages = 0
            
            for page in reader.pages:
                text = page.extract_text()
                if text and len(text.strip()) > 10:  # 如果有可读文本
                    text_pages += 1
            
            text_ratio = text_pages / total_pages
            
            # 如果大部分页面都有可提取文本，则认为是可转换PDF
            if text_ratio > threshold:
                return False
    except:
        traceback.print_exc()

test_path = "test_data/宁银理财费用变更样例"
pdf_fns = [x for x in os.listdir(test_path) if x.lower().endswith('pdf')]

pdf_fn = "关于部分产品固定管理费和销售服务费优惠的公告2025053002(1.23).pdf"

markdown_data = pdf_to_markdown('../大模型样例/POC脱敏材料/费率优惠公告（公开文件）/关于部分产品固定管理费和销售服务费优惠的公告2025053002.pdf_1750058152134.pdf' + pdf_fn)

pdf_fn = "test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf"

temp_fn = f"{uuid.uuid1().hex}.docx"
# 使用示例
pdf_to_word_pdf2docx(pdf_fn, temp_fn)
# 加载Word文档
doc = docx.Document(temp_fn)

out_markdown = ""

table_list = []
now_table = None
header = []
child_i = 0
table_id = 0
for child in doc.element.body:
    print(child_i, type(child))
    if isinstance(child, docx.oxml.text.paragraph.CT_P):  # 段落
        if child.text.replace('\n', '').strip() == '':
            continue
        if now_table is not None:
            print(child.text.replace('\n', '').strip())
            print("重置表格")
            out_markdown += "\n```json\n" + df_to_json_rows(fix_merge_table(now_table)) + "\n```\n"
            now_table = None
            header = []
            table_id = 0
        out_markdown += child.text.replace('\n', '').strip() + '\n'
    elif isinstance(child, docx.oxml.table.CT_Tbl): # 表格
        data = []
        table = Table(child, doc)
        last_tc = {}
        for row in table.rows:
            row_data = []
            cols_id = 0
            for cell in row.cells:
                tc = cell._tc  # 获取底层 XML 元素
                # 检查是否是合并单元格的后续行
                if check_vertical_merge(cell) == "restart":
                    if last_tc.get(cols_id, None) is None:
                        last_tc[cols_id] = tc
                    else:
                        row_data.append('!restart!')  # 非首行合并单元格，输出空
                        continue

                # 否则提取文本并清理
                text = cell.text.strip().replace('\n', '').replace('\t', ' ')
                row_data.append(text)
                cols_id += 1
            data.append(row_data)

        if now_table is None:
            header = data[0]
            data = data[1:]
        out_df = pd.DataFrame(data, columns=header)
        out_df['table_id'] = table_id
        if now_table is None:
            now_table = out_df.copy()
        else:
            now_table = pd.concat([now_table, out_df])
        table_id += 1
    child_i += 1

for pdf_fn in tqdm.tqdm(pdf_fns):
    full_path = f"{test_path}/{pdf_fn}"
    if is_scanned_pdf(full_path):
        raise
    else:
        input_file_path = f"{test_path}/{pdf_fn}"
        markdown_data = pdf_to_markdown(input_file_path)
        out_fn = pdf_fn.split('.')[0]
        with open(f'out_data/{out_fn}.md', 'w', encoding='utf-8') as f:
            f.write(markdown_data)





