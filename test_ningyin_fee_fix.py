#!/usr/bin/env python3
"""
测试宁银费率变更分析修复后的功能
"""

import json
import os
import sys

def test_ningyin_fee_analysis_v11():
    """测试宁银费用分析V1.1函数"""
    try:
        from func import ningyin_fee_analysis_v11
        print("✅ ningyin_fee_analysis_v11 函数导入成功")
        
        # 测试默认结果结构
        test_path = "non_existent_file.pdf"
        result = ningyin_fee_analysis_v11(test_path)
        
        # 验证返回结构
        expected_keys = ["product_info", "fee_changes", "effective_date", "announcement_date", "issuer"]
        
        for key in expected_keys:
            if key not in result:
                print(f"❌ 缺少必需字段: {key}")
                return False
            else:
                print(f"✅ 字段 {key} 存在")
        
        # 验证数据类型
        if not isinstance(result["product_info"], list):
            print("❌ product_info 应该是列表类型")
            return False
        
        if not isinstance(result["fee_changes"], list):
            print("❌ fee_changes 应该是列表类型")
            return False
        
        print("✅ 数据结构验证通过")
        return True
        
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_old_function_removed():
    """测试旧函数是否已移除"""
    try:
        from func import ningyin_fee_change_analysis
        print("❌ 旧函数 ningyin_fee_change_analysis 仍然存在，应该已被移除")
        return False
    except ImportError:
        print("✅ 旧函数 ningyin_fee_change_analysis 已成功移除")
        return True
    except Exception as e:
        print(f"❌ 测试旧函数移除时出错: {e}")
        return False

def test_api_configuration():
    """测试API配置是否正确"""
    try:
        # 模拟API配置检查
        expected_format = {
            "product_info": [{"product_name": "产品名称", "product_code": "产品代码"}],
            "fee_changes": [
                {
                    "fee_type": "费用类型",
                    "original_rate": "原费率", 
                    "new_rate": "新费率",
                    "effective_start": "生效开始日期",
                    "effective_end": "生效结束日期",
                    "notes": "备注信息"
                }
            ],
            "effective_date": "生效日期",
            "announcement_date": "公告日期",
            "issuer": "宁银理财有限责任公司"
        }
        
        print("✅ API配置格式验证:")
        print(json.dumps(expected_format, indent=2, ensure_ascii=False))
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        return False

def test_data_structure_consistency():
    """测试数据结构一致性"""
    try:
        from func import ningyin_fee_analysis_v11
        
        # 测试函数返回的默认结构
        result = ningyin_fee_analysis_v11("non_existent_file.pdf")
        
        # 检查与API期望格式的一致性
        api_expected_keys = ["product_info", "fee_changes", "effective_date", "announcement_date", "issuer"]
        function_keys = list(result.keys())
        
        missing_keys = set(api_expected_keys) - set(function_keys)
        extra_keys = set(function_keys) - set(api_expected_keys)
        
        if missing_keys:
            print(f"❌ 函数缺少API期望的字段: {missing_keys}")
            return False
        
        if extra_keys:
            print(f"⚠️  函数包含额外字段: {extra_keys}")
        
        print("✅ 数据结构与API期望一致")
        return True
        
    except Exception as e:
        print(f"❌ 数据结构一致性测试失败: {e}")
        return False

def test_notebook_logic_consistency():
    """测试与notebook逻辑的一致性"""
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        # 获取函数源码
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查关键逻辑是否存在
        key_components = [
            "is_scanned_pdf_simple",
            "pdf_to_markdown_ningyin", 
            "check_vertical_merge",
            "fix_merge_table",
            "pdf2docx"
        ]
        
        missing_components = []
        for component in key_components:
            if component not in source:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 缺少notebook关键逻辑组件: {missing_components}")
            return False
        
        print("✅ 包含所有notebook关键逻辑组件")
        return True
        
    except Exception as e:
        print(f"❌ notebook逻辑一致性测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试宁银费率变更分析修复...")
    print("=" * 60)
    
    tests = [
        ("函数导入和结构测试", test_ningyin_fee_analysis_v11),
        ("旧函数移除测试", test_old_function_removed),
        ("API配置测试", test_api_configuration),
        ("数据结构一致性测试", test_data_structure_consistency),
        ("Notebook逻辑一致性测试", test_notebook_logic_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！宁银费率变更分析已成功修复")
        print("\n📋 修复总结:")
        print("✅ 统一了数据结构格式（英文字段）")
        print("✅ 更新了API配置与函数输出的一致性")
        print("✅ 移除了旧版本函数避免混淆")
        print("✅ 保持了与notebook逻辑的完全一致性")
        print("✅ 优化了错误处理和结果验证")
    else:
        print("⚠️  部分测试失败，请检查修复情况")

if __name__ == "__main__":
    main()
