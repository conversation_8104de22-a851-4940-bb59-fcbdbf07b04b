#!/usr/bin/env python3
"""
测试内容识别功能是否正常工作
"""

def test_content_recognition():
    """测试内容识别功能"""
    print("🧪 测试内容识别功能...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        # 检查函数是否包含AI分析
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查是否包含必要的AI分析组件
        ai_components = [
            "ChatBot",
            "chat(",
            "markdown_json_to_dict",
            "system_prompt"
        ]
        
        missing_components = []
        for component in ai_components:
            if component not in source:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 缺少AI分析组件: {missing_components}")
            return False
        
        print("✅ 包含所有必要的AI分析组件")
        
        # 检查是否没有随机性参数
        randomness_params = [
            "temperature=",
            "top_p=",
            "max_tokens="
        ]
        
        found_params = []
        for param in randomness_params:
            if param in source:
                found_params.append(param)
        
        if found_params:
            print(f"❌ 仍包含随机性参数: {found_params}")
            return False
        
        print("✅ 没有随机性参数设置")
        
        # 测试函数调用
        result = ningyin_fee_analysis_v11("non_existent_file.pdf")
        
        # 检查返回结构
        expected_keys = ["product_info", "fee_changes", "effective_date", "announcement_date", "issuer"]
        
        for key in expected_keys:
            if key not in result:
                print(f"❌ 缺少必需字段: {key}")
                return False
        
        print("✅ 返回结构正确")
        print("✅ 内容识别功能已恢复")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_notebook_compliance():
    """测试notebook合规性"""
    print("🧪 测试notebook合规性...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查是否调用了纯notebook实现
        if "ningyin_fee_analysis_notebook_pure" not in source:
            print("❌ 没有调用纯notebook实现")
            return False
        
        print("✅ 调用了纯notebook实现")
        
        # 检查AI调用是否简洁（使用默认参数）
        if "ChatBot(" in source:
            # 查找ChatBot调用
            lines = source.split('\n')
            chatbot_lines = []
            for line in lines:
                if "ChatBot(" in line or "model=" in line or "system_prompt=" in line:
                    chatbot_lines.append(line.strip())
            
            # 检查是否只有model和system_prompt参数
            chatbot_call = '\n'.join(chatbot_lines)
            if "temperature" in chatbot_call or "top_p" in chatbot_call or "max_tokens" in chatbot_call:
                print("❌ ChatBot调用包含随机性参数")
                return False
            
            print("✅ ChatBot调用使用默认参数")
        
        # 检查chat调用是否简洁
        if "chatbot.chat(" in source:
            # 查找chat调用
            lines = source.split('\n')
            for line in lines:
                if "chatbot.chat(" in line:
                    if "temperature" in line or "top_p" in line or "max_tokens" in line:
                        print("❌ chat调用包含随机性参数")
                        return False
            
            print("✅ chat调用使用默认参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_function_architecture():
    """测试函数架构"""
    print("🧪 测试函数架构...")
    
    try:
        from func import ningyin_fee_analysis_v11, ningyin_fee_analysis_notebook_pure
        import inspect
        
        # 检查V1.1函数长度
        v11_source = inspect.getsource(ningyin_fee_analysis_v11)
        v11_lines = [line for line in v11_source.split('\n') if line.strip()]
        
        # 检查纯notebook函数长度
        pure_source = inspect.getsource(ningyin_fee_analysis_notebook_pure)
        pure_lines = [line for line in pure_source.split('\n') if line.strip()]
        
        print(f"📊 V1.1函数: {len(v11_lines)} 行代码")
        print(f"📊 纯notebook函数: {len(pure_lines)} 行代码")
        
        # V1.1函数应该相对简洁，主要是调用纯实现+AI分析
        if len(v11_lines) > 100:
            print("⚠️  V1.1函数可能过于复杂")
        else:
            print("✅ V1.1函数复杂度合理")
        
        # 纯notebook函数应该包含完整的PDF处理逻辑
        if len(pure_lines) < 50:
            print("❌ 纯notebook函数可能不完整")
            return False
        else:
            print("✅ 纯notebook函数包含完整逻辑")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("测试内容识别功能修复...")
    print("=" * 60)
    
    tests = [
        ("内容识别功能测试", test_content_recognition),
        ("Notebook合规性测试", test_notebook_compliance),
        ("函数架构测试", test_function_architecture)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 内容识别功能已修复！")
        print("\n📋 修复总结:")
        print("✅ 恢复了AI内容分析功能")
        print("✅ 保持了notebook合规性（无随机性参数）")
        print("✅ 使用纯notebook实现进行PDF处理")
        print("✅ 使用默认AI参数进行内容提取")
        print("✅ 函数架构清晰合理")
        print("\n🔧 现在的工作流程:")
        print("1. 使用纯notebook逻辑处理PDF")
        print("2. 使用AI默认参数分析内容")
        print("3. 返回结构化的JSON结果")
    else:
        print("⚠️  部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
