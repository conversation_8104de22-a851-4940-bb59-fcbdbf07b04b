#!/usr/bin/env python3
"""
测试 util_ai.py 修复后的功能
"""

def test_import():
    """测试导入是否正常"""
    try:
        from util_ai import ChatBot
        print("✅ util_ai.py 导入成功")
        return True
    except ImportError as e:
        print(f"❌ util_ai.py 导入失败: {e}")
        return False

def test_process_multiple_images_import():
    """测试 process_multiple_images 函数导入"""
    try:
        from util_img_process import process_multiple_images
        print("✅ process_multiple_images 函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ process_multiple_images 函数导入失败: {e}")
        return False

def test_chatbot_creation():
    """测试 ChatBot 实例化"""
    try:
        from util_ai import ChatBot
        bot = ChatBot()
        print("✅ ChatBot 实例化成功")
        return True
    except Exception as e:
        print(f"❌ ChatBot 实例化失败: {e}")
        return False

def test_chat_with_img_method_signature():
    """测试 chat_with_img 方法签名"""
    try:
        from util_ai import ChatBot
        bot = ChatBot()
        
        # 检查方法是否存在
        if hasattr(bot, 'chat_with_img'):
            print("✅ chat_with_img 方法存在")
            
            # 检查方法签名（通过尝试调用来验证参数要求）
            try:
                # 这应该会因为缺少必需的 img_url 参数而失败
                bot.chat_with_img("test message")
                print("❌ chat_with_img 方法签名不正确 - img_url 应该是必需参数")
                return False
            except TypeError as te:
                if "img_url" in str(te):
                    print("✅ chat_with_img 方法签名正确 - img_url 是必需参数")
                    return True
                else:
                    print(f"❌ chat_with_img 方法签名错误: {te}")
                    return False
            except Exception as e:
                # 其他异常（如参数验证错误）也是可以接受的
                print(f"✅ chat_with_img 方法签名正确 - 参数验证正常: {type(e).__name__}")
                return True
        else:
            print("❌ chat_with_img 方法不存在")
            return False
    except Exception as e:
        print(f"❌ 测试 chat_with_img 方法签名失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试 util_ai.py 修复...")
    print("=" * 50)
    
    tests = [
        test_import,
        test_process_multiple_images_import,
        test_chatbot_creation,
        test_chat_with_img_method_signature
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！util_ai.py 已成功修复为 util_ai_real.py 的版本")
    else:
        print("⚠️  部分测试失败，请检查修复情况")

if __name__ == "__main__":
    main()
