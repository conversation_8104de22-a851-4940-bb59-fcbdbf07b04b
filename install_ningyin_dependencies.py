#!/usr/bin/env python3
"""
安装宁银费率变更分析所需的依赖包
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """检查并安装包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name} 安装失败: {e}")
            return False

def main():
    """安装所需依赖"""
    print("检查宁银费率变更分析所需依赖...")
    print("=" * 50)
    
    # 必需的依赖包
    dependencies = [
        ("PyPDF2", "PyPDF2"),
        ("pdf2docx", "pdf2docx"),
        ("python-docx", "docx"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl")
    ]
    
    success_count = 0
    total_count = len(dependencies)
    
    for package_name, import_name in dependencies:
        if check_and_install_package(package_name, import_name):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"依赖安装结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有依赖安装成功！")
        print("\n现在可以正常使用宁银费率变更分析功能了。")
    else:
        print("⚠️  部分依赖安装失败，可能影响功能使用")
        print("\n请手动安装失败的依赖包：")
        print("pip install PyPDF2 pdf2docx python-docx pandas openpyxl")

if __name__ == "__main__":
    main()
