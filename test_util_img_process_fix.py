#!/usr/bin/env python3
"""
测试 util_img_process.py 修复后的功能
"""

def test_import():
    """测试导入是否正常"""
    try:
        import util_img_process
        print("✅ util_img_process.py 导入成功")
        return True
    except ImportError as e:
        print(f"❌ util_img_process.py 导入失败: {e}")
        return False

def test_process_multiple_images_function():
    """测试 process_multiple_images 函数是否存在"""
    try:
        from util_img_process import process_multiple_images
        print("✅ process_multiple_images 函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ process_multiple_images 函数导入失败: {e}")
        return False

def test_process_base64_image_function():
    """测试 process_base64_image 函数是否存在"""
    try:
        from util_img_process import process_base64_image
        print("✅ process_base64_image 函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ process_base64_image 函数导入失败: {e}")
        return False

def test_chat_bot_img_function():
    """测试 chat_bot_img 函数是否存在"""
    try:
        from util_img_process import chat_bot_img
        print("✅ chat_bot_img 函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ chat_bot_img 函数导入失败: {e}")
        return False

def test_chat_bot_img_single_function():
    """测试 chat_bot_img_single 函数是否存在"""
    try:
        from util_img_process import chat_bot_img_single
        print("✅ chat_bot_img_single 函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ chat_bot_img_single 函数导入失败: {e}")
        return False

def test_enhance_functions():
    """测试图片增强相关函数是否存在"""
    try:
        from util_img_process import enhance_image_quality, enhance_pdf_file, enhance_image_file, enhance_file_quality
        print("✅ 图片增强函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ 图片增强函数导入失败: {e}")
        return False

def test_function_signatures():
    """测试函数签名是否正确"""
    try:
        from util_img_process import process_multiple_images, chat_bot_img
        import inspect
        
        # 检查 process_multiple_images 函数签名
        sig = inspect.signature(process_multiple_images)
        params = list(sig.parameters.keys())
        expected_params = ['img_inputs', 'max_pixels', 'fixed_height', 'max_height_ratio']
        
        if params == expected_params:
            print("✅ process_multiple_images 函数签名正确")
        else:
            print(f"❌ process_multiple_images 函数签名不正确，期望: {expected_params}，实际: {params}")
            return False
        
        # 检查 chat_bot_img 函数签名
        sig = inspect.signature(chat_bot_img)
        params = list(sig.parameters.keys())
        if 'prompt' in params and 'img_url' in params:
            print("✅ chat_bot_img 函数签名正确")
        else:
            print(f"❌ chat_bot_img 函数签名不正确，缺少必要参数")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 测试函数签名失败: {e}")
        return False

def compare_with_real_file():
    """比较关键函数是否与 util_img_process_real.py 一致"""
    try:
        # 检查关键函数是否存在
        from util_img_process import (
            process_multiple_images, 
            process_base64_image, 
            chat_bot_img, 
            chat_bot_img_single,
            enhance_image_quality,
            enhance_pdf_file,
            enhance_image_file,
            enhance_file_quality
        )
        
        print("✅ 所有关键函数都存在，与 util_img_process_real.py 基本一致")
        return True
    except ImportError as e:
        print(f"❌ 缺少关键函数: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始测试 util_img_process.py 修复...")
    print("=" * 60)
    
    tests = [
        test_import,
        test_process_multiple_images_function,
        test_process_base64_image_function,
        test_chat_bot_img_function,
        test_chat_bot_img_single_function,
        test_enhance_functions,
        test_function_signatures,
        compare_with_real_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！util_img_process.py 已成功修复为 util_img_process_real.py 的版本")
    else:
        print("⚠️  部分测试失败，请检查修复情况")

if __name__ == "__main__":
    main()
