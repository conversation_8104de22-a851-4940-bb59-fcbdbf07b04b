#!/usr/bin/env python3
"""
测试宁银费率变更分析是否遵循notebook规范
"""

def test_no_ai_randomness_params():
    """测试是否移除了AI随机性参数"""
    print("🧪 测试AI随机性参数...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查是否移除了随机性参数
        randomness_params = [
            "temperature=0.1",
            "temperature=0.3", 
            "top_p=0.3",
            "top_p=0.1"
        ]
        
        found_params = []
        for param in randomness_params:
            if param in source:
                found_params.append(param)
        
        if found_params:
            print(f"❌ 仍然包含随机性参数设置: {found_params}")
            print("   notebook中没有AI随机性设置，应该移除这些参数")
            return False
        
        print("✅ 已移除AI随机性参数设置，遵循notebook规范")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_increased_content_limit():
    """测试是否增加了字数限制"""
    print("🧪 测试字数限制...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查字数限制
        if "12000" in source:
            print("✅ 字数限制已增加到12000字符")
            return True
        elif "8000" in source:
            print("⚠️  字数限制为8000字符，可以进一步增加")
            return True
        elif "4000" in source:
            print("❌ 字数限制仍为4000字符，需要增加")
            return False
        else:
            print("⚠️  未找到明确的字数限制设置")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_notebook_consistency():
    """测试与notebook的一致性"""
    print("🧪 测试与notebook的一致性...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查notebook核心组件
        notebook_components = [
            "pdf2docx",
            "Converter", 
            "check_vertical_merge",
            "fix_merge_table",
            "docx.Document",
            "uuid.uuid1().hex"
        ]
        
        missing_components = []
        for component in notebook_components:
            if component not in source:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 缺少notebook核心组件: {missing_components}")
            return False
        
        print("✅ 包含所有notebook核心组件")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ai_call_simplicity():
    """测试AI调用的简洁性"""
    print("🧪 测试AI调用简洁性...")
    
    try:
        from func import ningyin_fee_analysis_v11
        import inspect
        
        source = inspect.getsource(ningyin_fee_analysis_v11)
        
        # 检查AI调用是否简洁（不包含复杂的参数设置）
        if "chatbot.chat(" in source:
            # 查找chat调用
            chat_calls = []
            lines = source.split('\n')
            for i, line in enumerate(lines):
                if "chatbot.chat(" in line:
                    # 收集这个调用的所有行
                    call_lines = [line]
                    j = i + 1
                    while j < len(lines) and not lines[j].strip().endswith(')'):
                        call_lines.append(lines[j])
                        j += 1
                    if j < len(lines):
                        call_lines.append(lines[j])
                    chat_calls.append('\n'.join(call_lines))
            
            # 检查是否有复杂的参数设置
            for call in chat_calls:
                if any(param in call for param in ["temperature=", "top_p=", "max_tokens="]):
                    print("❌ AI调用包含随机性参数，不符合notebook简洁性")
                    print(f"   调用: {call[:100]}...")
                    return False
            
            print("✅ AI调用简洁，符合notebook风格")
            return True
        else:
            print("⚠️  未找到AI调用")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_output_format():
    """测试输出格式"""
    print("🧪 测试输出格式...")
    
    try:
        result = ningyin_fee_analysis_v11("non_existent_file.pdf")
        
        # 检查输出格式
        expected_keys = ["product_info", "fee_changes", "effective_date", "announcement_date", "issuer"]
        
        for key in expected_keys:
            if key not in result:
                print(f"❌ 缺少必需字段: {key}")
                return False
        
        print("✅ 输出格式正确")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("测试宁银费率变更分析的notebook合规性...")
    print("=" * 60)
    
    tests = [
        ("AI随机性参数测试", test_no_ai_randomness_params),
        ("字数限制测试", test_increased_content_limit),
        ("Notebook一致性测试", test_notebook_consistency),
        ("AI调用简洁性测试", test_ai_call_simplicity),
        ("输出格式测试", test_output_format)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！完全遵循notebook规范")
        print("\n📋 合规性确认:")
        print("✅ 移除了AI随机性参数设置")
        print("✅ 增加了字数限制")
        print("✅ 保持了notebook核心逻辑")
        print("✅ AI调用简洁明了")
        print("✅ 输出格式正确")
    else:
        print("⚠️  部分测试失败，请检查合规性")

if __name__ == "__main__":
    main()
